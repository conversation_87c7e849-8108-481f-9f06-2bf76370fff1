# 🚀 Deployment Guide

This guide explains how to deploy the Posture Detection App to various platforms.

## 📋 Prerequisites

- Git repository with the code
- Accounts on deployment platforms (Railway, Vercel, etc.)
- Environment variables configured

## 🚂 Backend Deployment (Railway)

Railway is recommended for the backend due to its excellent support for Bun and Node.js applications.

### Step 1: Prepare Repository
1. Ensure your code is pushed to GitHub/GitLab
2. Make sure `railway.toml` is in the root directory
3. Verify `backend/package.json` has correct scripts

### Step 2: Deploy to Railway
1. Visit [railway.app](https://railway.app)
2. Sign up/login with GitHub
3. Click "New Project" → "Deploy from GitHub repo"
4. Select your repository
5. Railway will automatically detect the configuration from `railway.toml`
6. Set environment variables:
   - `PORT=3001`
   - `NODE_ENV=production`
7. Deploy and wait for build completion

### Step 3: Get Backend URL
- Copy the generated Railway URL (e.g., `https://your-app.railway.app`)
- This will be used for frontend configuration

## 🔺 Frontend Deployment (Vercel)

Vercel is ideal for React applications with excellent performance and CDN.

### Step 1: Configure Environment
1. Update `frontend/vercel.json` with your Railway backend URL
2. Set `REACT_APP_API_URL` to your Railway backend URL

### Step 2: Deploy to Vercel
1. Visit [vercel.com](https://vercel.com)
2. Sign up/login with GitHub
3. Click "New Project"
4. Import your repository
5. Set root directory to `frontend`
6. Configure environment variables:
   - `REACT_APP_API_URL=https://your-backend.railway.app`
7. Deploy

## 🔧 Alternative Deployment Options

### Backend Alternatives
- **Render**: Similar to Railway, good Bun support
- **Heroku**: Classic choice, may need buildpack for Bun
- **AWS/GCP/Azure**: For enterprise deployments

### Frontend Alternatives
- **Netlify**: Great for static sites
- **GitHub Pages**: Free option for public repos
- **AWS S3 + CloudFront**: Enterprise solution

## 🌍 Environment Variables

### Production Environment Variables

**Backend (.env)**
```
PORT=3001
NODE_ENV=production
```

**Frontend (Vercel Environment Variables)**
```
REACT_APP_API_URL=https://your-backend-url.railway.app
```

## 🔍 Health Checks

After deployment, verify:

1. **Backend Health**: `GET https://your-backend.railway.app/health`
2. **Frontend Loading**: Visit your Vercel URL
3. **API Integration**: Test pose detection functionality

## 🐛 Troubleshooting

### Common Issues

**Backend Issues:**
- Build failures: Check Bun version compatibility
- Memory issues: Increase Railway plan if needed
- TensorFlow.js issues: Ensure proper Node.js version

**Frontend Issues:**
- CORS errors: Verify backend CORS configuration
- API connection: Check environment variable configuration
- Build failures: Verify all dependencies are installed

### Debug Steps
1. Check deployment logs on Railway/Vercel
2. Verify environment variables are set correctly
3. Test API endpoints individually
4. Check browser console for frontend errors

## 📊 Monitoring

### Recommended Monitoring
- **Railway**: Built-in metrics and logs
- **Vercel**: Analytics and performance monitoring
- **External**: Consider Sentry for error tracking

## 🔄 CI/CD Pipeline

For automated deployments:

1. **GitHub Actions** for testing
2. **Railway** auto-deploys on push to main
3. **Vercel** auto-deploys on push to main

Example GitHub Actions workflow:
```yaml
name: Test and Deploy
on:
  push:
    branches: [main]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: oven-sh/setup-bun@v1
      - run: bun install
      - run: bun test
```

## 🎯 Performance Optimization

### Backend Optimization
- Enable gzip compression
- Use Railway's built-in caching
- Optimize TensorFlow.js model loading

### Frontend Optimization
- Vercel's automatic optimizations
- Image optimization
- Code splitting for large bundles

---

For any deployment issues, refer to the platform-specific documentation or create an issue in the repository.
