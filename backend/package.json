{"name": "backend", "module": "index.ts", "type": "module", "private": true, "scripts": {"dev": "bun --hot index.ts", "start": "bun index.ts", "build": "bun build index.ts --outdir ./dist"}, "devDependencies": {"@types/bun": "latest"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@tensorflow-models/pose-detection": "^2.1.3", "@tensorflow/tfjs-node": "^4.22.0", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/multer": "^2.0.0", "cors": "^2.8.5", "express": "^5.1.0", "multer": "^2.0.1"}}