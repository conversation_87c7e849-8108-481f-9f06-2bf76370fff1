import express from "express";
import cors from "cors";
import multer from "multer";
import path from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Middleware
app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/");
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
});

// Create uploads directory if it doesn't exist
import fs from "fs";
if (!fs.existsSync("uploads")) {
  fs.mkdirSync("uploads");
}

// Routes
app.get("/", (req, res) => {
  res.json({ message: "Posture Detection Backend API" });
});

app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Proxy requests to Python pose detection service
app.post("/api/analyze-frame", async (req, res) => {
  try {
    const response = await fetch("http://localhost:8001/analyze-frame", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        image_data: req.body.image_data,
        posture_type: req.body.posture_type || "auto",
      }),
    });

    const result = await response.json();
    res.json(result);
  } catch (error) {
    console.error("Error communicating with pose detection service:", error);
    res.status(500).json({
      error: "Failed to analyze frame",
      details: error.message,
    });
  }
});

app.post("/api/analyze-video", upload.single("video"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No video file provided" });
    }

    const formData = new FormData();
    const fileBuffer = fs.readFileSync(req.file.path);
    const blob = new Blob([fileBuffer], { type: req.file.mimetype });

    formData.append("video", blob, req.file.originalname);
    formData.append("posture_type", req.body.posture_type || "auto");

    const response = await fetch("http://localhost:8001/analyze-video", {
      method: "POST",
      body: formData,
    });

    const result = await response.json();

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json(result);
  } catch (error) {
    console.error("Error processing video:", error);
    res.status(500).json({
      error: "Failed to analyze video",
      details: error.message,
    });
  }
});

app.listen(PORT, () => {
  console.log(`Backend server running on port ${PORT}`);
  console.log(`Health check: http://localhost:${PORT}/health`);
});
