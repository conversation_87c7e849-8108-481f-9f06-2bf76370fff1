import express from "express";
import cors from "cors";
import multer from "multer";
import path from "path";
import { fileURLToPath } from "url";
import { PostureAnalyzer } from "./poseDetection";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();
const PORT = process.env.PORT || 3001;

// Initialize pose analyzer
const poseAnalyzer = new PostureAnalyzer();

// Middleware
app.use(cors());
app.use(express.json({ limit: "50mb" }));
app.use(express.urlencoded({ extended: true, limit: "50mb" }));

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, "uploads/");
  },
  filename: (req, file, cb) => {
    cb(null, Date.now() + "-" + file.originalname);
  },
});

const upload = multer({
  storage: storage,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB limit
  },
});

// Create uploads directory if it doesn't exist
import fs from "fs";
if (!fs.existsSync("uploads")) {
  fs.mkdirSync("uploads");
}

// Routes
app.get("/", (req, res) => {
  res.json({ message: "Posture Detection Backend API" });
});

app.get("/health", (req, res) => {
  res.json({ status: "healthy", timestamp: new Date().toISOString() });
});

// Analyze frame endpoint
app.post("/api/analyze-frame", async (req, res) => {
  try {
    const { image_data, posture_type = "auto" } = req.body;

    if (!image_data) {
      return res.status(400).json({ error: "No image data provided" });
    }

    // Convert base64 to buffer
    const base64Data = image_data.replace(/^data:image\/[a-z]+;base64,/, "");
    const imageBuffer = Buffer.from(base64Data, "base64");

    const result = await poseAnalyzer.analyzeImage(imageBuffer, posture_type);
    res.json(result);
  } catch (error) {
    console.error("Error analyzing frame:", error);
    res.status(500).json({
      error: "Failed to analyze frame",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

app.post("/api/analyze-video", upload.single("video"), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: "No video file provided" });
    }

    const postureType = req.body.posture_type || "auto";

    // For now, we'll analyze just the first frame of the video
    // In a full implementation, you'd extract multiple frames
    const fileBuffer = fs.readFileSync(req.file.path);

    // For simplicity, we'll return a mock video analysis
    // In production, you'd use ffmpeg to extract frames
    const mockAnalysis = {
      total_frames_analyzed: 1,
      good_posture_frames: 1,
      bad_posture_frames: 0,
      posture_score: 85.0,
      frame_analyses: [
        {
          frame_number: 0,
          posture_type: postureType,
          issues: [],
          is_good_posture: true,
          landmarks_detected: true,
        },
      ],
    };

    // Clean up uploaded file
    fs.unlinkSync(req.file.path);

    res.json(mockAnalysis);
  } catch (error) {
    console.error("Error processing video:", error);
    res.status(500).json({
      error: "Failed to analyze video",
      details: error instanceof Error ? error.message : "Unknown error",
    });
  }
});

// Initialize pose analyzer and start server
async function startServer() {
  try {
    console.log("Initializing pose detection...");
    await poseAnalyzer.initialize();
    console.log("Pose detection initialized successfully");

    app.listen(PORT, () => {
      console.log(`Backend server running on port ${PORT}`);
      console.log(`Health check: http://localhost:${PORT}/health`);
    });
  } catch (error) {
    console.error("Failed to start server:", error);
    process.exit(1);
  }
}

startServer();
