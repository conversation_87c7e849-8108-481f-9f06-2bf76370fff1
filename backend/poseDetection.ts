import * as tf from '@tensorflow/tfjs-node';
import * as poseDetection from '@tensorflow-models/pose-detection';

interface PostureAnalysis {
  posture_type: string;
  issues: string[];
  is_good_posture: boolean;
  knee_angle?: number;
  back_angle?: number;
  neck_angle?: number;
  landmarks_detected: boolean;
  error?: string;
}

class PostureAnalyzer {
  private detector: poseDetection.PoseDetector | null = null;

  async initialize() {
    try {
      // Initialize TensorFlow backend
      await tf.ready();
      
      // Create pose detector
      const model = poseDetection.SupportedModels.MoveNet;
      this.detector = await poseDetection.createDetector(model, {
        modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING
      });
      
      console.log('Pose detector initialized successfully');
    } catch (error) {
      console.error('Failed to initialize pose detector:', error);
      throw error;
    }
  }

  private calculateAngle(a: [number, number], b: [number, number], c: [number, number]): number {
    const radians = Math.atan2(c[1] - b[1], c[0] - b[0]) - Math.atan2(a[1] - b[1], a[0] - b[0]);
    let angle = Math.abs(radians * 180.0 / Math.PI);
    
    if (angle > 180.0) {
      angle = 360 - angle;
    }
    
    return angle;
  }

  private analyzeSquatPosture(keypoints: poseDetection.Keypoint[]): PostureAnalysis {
    const issues: string[] = [];
    
    // Get key landmarks
    const leftHip = keypoints.find(kp => kp.name === 'left_hip');
    const leftKnee = keypoints.find(kp => kp.name === 'left_knee');
    const leftAnkle = keypoints.find(kp => kp.name === 'left_ankle');
    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');
    
    const rightHip = keypoints.find(kp => kp.name === 'right_hip');
    const rightKnee = keypoints.find(kp => kp.name === 'right_knee');
    const rightAnkle = keypoints.find(kp => kp.name === 'right_ankle');
    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');

    if (!leftHip || !leftKnee || !leftAnkle || !leftShoulder || 
        !rightHip || !rightKnee || !rightAnkle || !rightShoulder) {
      return {
        posture_type: 'squat',
        issues: ['Could not detect all required body landmarks'],
        is_good_posture: false,
        landmarks_detected: false
      };
    }

    // Check knee over toe (knee should not go beyond ankle in x-direction)
    if (leftKnee.x < leftAnkle.x - 0.05) {
      issues.push('Left knee is going too far forward over toe');
    }
    if (rightKnee.x < rightAnkle.x - 0.05) {
      issues.push('Right knee is going too far forward over toe');
    }

    // Check back angle (should be relatively straight)
    const leftBackAngle = this.calculateAngle(
      [leftShoulder.x, leftShoulder.y],
      [leftHip.x, leftHip.y],
      [leftKnee.x, leftKnee.y]
    );
    const rightBackAngle = this.calculateAngle(
      [rightShoulder.x, rightShoulder.y],
      [rightHip.x, rightHip.y],
      [rightKnee.x, rightKnee.y]
    );
    
    if (leftBackAngle < 150 || rightBackAngle < 150) {
      issues.push('Back is too hunched forward - keep chest up');
    }

    // Check knee angle for depth
    const leftKneeAngle = this.calculateAngle(
      [leftHip.x, leftHip.y],
      [leftKnee.x, leftKnee.y],
      [leftAnkle.x, leftAnkle.y]
    );
    const rightKneeAngle = this.calculateAngle(
      [rightHip.x, rightHip.y],
      [rightKnee.x, rightKnee.y],
      [rightAnkle.x, rightAnkle.y]
    );
    
    const avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2;
    const avgBackAngle = (leftBackAngle + rightBackAngle) / 2;

    return {
      posture_type: 'squat',
      issues,
      knee_angle: avgKneeAngle,
      back_angle: avgBackAngle,
      is_good_posture: issues.length === 0,
      landmarks_detected: true
    };
  }

  private analyzeSittingPosture(keypoints: poseDetection.Keypoint[]): PostureAnalysis {
    const issues: string[] = [];
    
    // Get key landmarks
    const nose = keypoints.find(kp => kp.name === 'nose');
    const leftShoulder = keypoints.find(kp => kp.name === 'left_shoulder');
    const rightShoulder = keypoints.find(kp => kp.name === 'right_shoulder');
    const leftHip = keypoints.find(kp => kp.name === 'left_hip');
    const rightHip = keypoints.find(kp => kp.name === 'right_hip');

    if (!nose || !leftShoulder || !rightShoulder || !leftHip || !rightHip) {
      return {
        posture_type: 'sitting',
        issues: ['Could not detect all required body landmarks'],
        is_good_posture: false,
        landmarks_detected: false
      };
    }

    // Calculate neck angle (nose to shoulder line vs vertical)
    const avgShoulderX = (leftShoulder.x + rightShoulder.x) / 2;
    const avgShoulderY = (leftShoulder.y + rightShoulder.y) / 2;
    
    const neckAngle = Math.abs(Math.atan2(nose.x - avgShoulderX, avgShoulderY - nose.y) * 180 / Math.PI);
    
    if (neckAngle > 30) {
      issues.push('Neck is bent too far forward - adjust screen height');
    }

    // Check back straightness (shoulder to hip alignment)
    const avgHipX = (leftHip.x + rightHip.x) / 2;
    const avgHipY = (leftHip.y + rightHip.y) / 2;
    
    const backAngle = Math.abs(Math.atan2(avgShoulderX - avgHipX, avgHipY - avgShoulderY) * 180 / Math.PI);
    
    if (backAngle > 20) {
      issues.push('Back is not straight - sit up straighter');
    }

    return {
      posture_type: 'sitting',
      issues,
      neck_angle: neckAngle,
      back_angle: backAngle,
      is_good_posture: issues.length === 0,
      landmarks_detected: true
    };
  }

  async analyzeImage(imageBuffer: Buffer, postureType: string = 'auto'): Promise<PostureAnalysis> {
    if (!this.detector) {
      return {
        posture_type: 'unknown',
        issues: ['Pose detector not initialized'],
        is_good_posture: false,
        landmarks_detected: false,
        error: 'Detector not ready'
      };
    }

    try {
      // Decode image
      const imageTensor = tf.node.decodeImage(imageBuffer, 3);
      
      // Detect poses
      const poses = await this.detector.estimatePoses(imageTensor);
      
      // Clean up tensor
      imageTensor.dispose();

      if (poses.length === 0) {
        return {
          posture_type: postureType,
          issues: ['No person detected in image'],
          is_good_posture: false,
          landmarks_detected: false
        };
      }

      const pose = poses[0];
      const keypoints = pose.keypoints.filter(kp => kp.score && kp.score > 0.3);

      // Auto-detect posture type if not specified
      if (postureType === 'auto') {
        const leftKnee = keypoints.find(kp => kp.name === 'left_knee');
        const leftHip = keypoints.find(kp => kp.name === 'left_hip');
        const leftAnkle = keypoints.find(kp => kp.name === 'left_ankle');
        
        if (leftKnee && leftHip && leftAnkle) {
          const kneeAngle = this.calculateAngle(
            [leftHip.x, leftHip.y],
            [leftKnee.x, leftKnee.y],
            [leftAnkle.x, leftAnkle.y]
          );
          postureType = kneeAngle < 140 ? 'squat' : 'sitting';
        } else {
          postureType = 'sitting';
        }
      }

      if (postureType === 'squat') {
        return this.analyzeSquatPosture(keypoints);
      } else {
        return this.analyzeSittingPosture(keypoints);
      }

    } catch (error) {
      console.error('Error analyzing image:', error);
      return {
        posture_type: postureType,
        issues: ['Failed to analyze image'],
        is_good_posture: false,
        landmarks_detected: false,
        error: error.message
      };
    }
  }
}

export { PostureAnalyzer, PostureAnalysis };
