from fastapi import <PERSON><PERSON><PERSON>, File, UploadFile, Form
from fastapi.middleware.cors import CORSMiddleware
import cv2
import numpy as np
from pose_detector import PostureAnalyzer
import base64
from io import BytesIO
from PIL import Image

app = FastAPI(title="Posture Detection API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domain
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Initialize pose analyzer
pose_analyzer = PostureAnalyzer()

@app.get("/")
async def root():
    return {"message": "Posture Detection API is running"}

@app.post("/analyze-frame")
async def analyze_frame(
    image_data: str = Form(...),
    posture_type: str = Form(default="auto")
):
    """Analyze a single frame for posture"""
    try:
        # Decode base64 image
        image_data = image_data.split(',')[1] if ',' in image_data else image_data
        image_bytes = base64.b64decode(image_data)
        
        # Convert to OpenCV format
        pil_image = Image.open(BytesIO(image_bytes))
        cv_image = cv2.cvtColor(np.array(pil_image), cv2.COLOR_RGB2BGR)
        
        # Analyze posture
        result = pose_analyzer.process_frame(cv_image, posture_type)
        
        return result
        
    except Exception as e:
        return {"error": f"Failed to process frame: {str(e)}"}

@app.post("/analyze-video")
async def analyze_video(
    video: UploadFile = File(...),
    posture_type: str = Form(default="auto")
):
    """Analyze uploaded video for posture"""
    try:
        # Save uploaded video temporarily
        video_bytes = await video.read()
        
        # Write to temporary file
        temp_video_path = f"/tmp/{video.filename}"
        with open(temp_video_path, "wb") as f:
            f.write(video_bytes)
        
        # Process video
        cap = cv2.VideoCapture(temp_video_path)
        frame_analyses = []
        frame_count = 0
        
        while cap.isOpened():
            ret, frame = cap.read()
            if not ret:
                break
                
            # Analyze every 5th frame to reduce processing time
            if frame_count % 5 == 0:
                analysis = pose_analyzer.process_frame(frame, posture_type)
                analysis["frame_number"] = frame_count
                frame_analyses.append(analysis)
            
            frame_count += 1
            
            # Limit to first 300 frames (10 seconds at 30fps)
            if frame_count > 300:
                break
        
        cap.release()
        
        # Calculate summary statistics
        good_frames = sum(1 for analysis in frame_analyses 
                         if analysis.get("is_good_posture", False))
        total_frames = len(frame_analyses)
        
        return {
            "total_frames_analyzed": total_frames,
            "good_posture_frames": good_frames,
            "bad_posture_frames": total_frames - good_frames,
            "posture_score": (good_frames / total_frames * 100) if total_frames > 0 else 0,
            "frame_analyses": frame_analyses
        }
        
    except Exception as e:
        return {"error": f"Failed to process video: {str(e)}"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001)
