import cv2
import mediapipe as mp
import numpy as np
import math
from typing import Dict, List, Tuple, Optional

class PostureAnalyzer:
    def __init__(self):
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=1,
            enable_segmentation=False,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils

    def calculate_angle(self, a: Tuple[float, float], b: <PERSON><PERSON>[float, float], c: Tuple[float, float]) -> float:
        """Calculate angle between three points"""
        a = np.array(a)
        b = np.array(b)
        c = np.array(c)
        
        radians = np.arctan2(c[1] - b[1], c[0] - b[0]) - np.arctan2(a[1] - b[1], a[0] - b[0])
        angle = np.abs(radians * 180.0 / np.pi)
        
        if angle > 180.0:
            angle = 360 - angle
            
        return angle

    def analyze_squat_posture(self, landmarks) -> Dict:
        """Analyze squat posture and detect bad form"""
        issues = []
        
        # Get key landmarks
        left_hip = [landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].x,
                   landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].y]
        left_knee = [landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].x,
                    landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].y]
        left_ankle = [landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].x,
                     landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].y]
        left_shoulder = [landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].x,
                        landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].y]
        
        right_hip = [landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].x,
                    landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].y]
        right_knee = [landmarks[self.mp_pose.PoseLandmark.RIGHT_KNEE.value].x,
                     landmarks[self.mp_pose.PoseLandmark.RIGHT_KNEE.value].y]
        right_ankle = [landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE.value].x,
                      landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE.value].y]
        right_shoulder = [landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].x,
                         landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].y]

        # Check knee over toe (knee should not go beyond ankle in x-direction)
        if left_knee[0] < left_ankle[0] - 0.05:  # 5% tolerance
            issues.append("Left knee is going too far forward over toe")
        if right_knee[0] < right_ankle[0] - 0.05:
            issues.append("Right knee is going too far forward over toe")

        # Check back angle (should be relatively straight, around 150-180 degrees)
        left_back_angle = self.calculate_angle(left_shoulder, left_hip, left_knee)
        right_back_angle = self.calculate_angle(right_shoulder, right_hip, right_knee)
        
        if left_back_angle < 150 or right_back_angle < 150:
            issues.append("Back is too hunched forward - keep chest up")

        # Check knee angle for depth
        left_knee_angle = self.calculate_angle(left_hip, left_knee, left_ankle)
        right_knee_angle = self.calculate_angle(right_hip, right_knee, right_ankle)
        
        avg_knee_angle = (left_knee_angle + right_knee_angle) / 2
        
        return {
            "posture_type": "squat",
            "issues": issues,
            "knee_angle": avg_knee_angle,
            "back_angle": (left_back_angle + right_back_angle) / 2,
            "is_good_posture": len(issues) == 0
        }

    def analyze_sitting_posture(self, landmarks) -> Dict:
        """Analyze sitting posture and detect bad form"""
        issues = []
        
        # Get key landmarks
        nose = [landmarks[self.mp_pose.PoseLandmark.NOSE.value].x,
               landmarks[self.mp_pose.PoseLandmark.NOSE.value].y]
        left_shoulder = [landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].x,
                        landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER.value].y]
        right_shoulder = [landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].x,
                         landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER.value].y]
        left_hip = [landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].x,
                   landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].y]
        right_hip = [landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].x,
                    landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP.value].y]

        # Calculate neck angle (nose to shoulder line vs vertical)
        avg_shoulder = [(left_shoulder[0] + right_shoulder[0]) / 2,
                       (left_shoulder[1] + right_shoulder[1]) / 2]
        
        # Calculate angle between nose-shoulder line and vertical
        neck_angle = abs(math.atan2(nose[0] - avg_shoulder[0], avg_shoulder[1] - nose[1]) * 180 / math.pi)
        
        if neck_angle > 30:
            issues.append("Neck is bent too far forward - adjust screen height")

        # Check back straightness (shoulder to hip alignment)
        avg_hip = [(left_hip[0] + right_hip[0]) / 2,
                  (left_hip[1] + right_hip[1]) / 2]
        
        back_angle = abs(math.atan2(avg_shoulder[0] - avg_hip[0], avg_hip[1] - avg_shoulder[1]) * 180 / math.pi)
        
        if back_angle > 20:
            issues.append("Back is not straight - sit up straighter")

        return {
            "posture_type": "sitting",
            "issues": issues,
            "neck_angle": neck_angle,
            "back_angle": back_angle,
            "is_good_posture": len(issues) == 0
        }

    def process_frame(self, image: np.ndarray, posture_type: str = "auto") -> Dict:
        """Process a single frame and return posture analysis"""
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_image)
        
        if not results.pose_landmarks:
            return {
                "error": "No pose detected in frame",
                "landmarks_detected": False
            }

        landmarks = results.pose_landmarks.landmark
        
        # Auto-detect posture type if not specified
        if posture_type == "auto":
            # Simple heuristic: if knees are significantly bent, assume squat
            left_knee_angle = self.calculate_angle(
                [landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].x,
                 landmarks[self.mp_pose.PoseLandmark.LEFT_HIP.value].y],
                [landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].x,
                 landmarks[self.mp_pose.PoseLandmark.LEFT_KNEE.value].y],
                [landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].x,
                 landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE.value].y]
            )
            posture_type = "squat" if left_knee_angle < 140 else "sitting"

        if posture_type == "squat":
            analysis = self.analyze_squat_posture(landmarks)
        else:
            analysis = self.analyze_sitting_posture(landmarks)

        # Add landmark coordinates for frontend visualization
        landmark_coords = []
        for landmark in landmarks:
            landmark_coords.append({
                "x": landmark.x,
                "y": landmark.y,
                "z": landmark.z,
                "visibility": landmark.visibility
            })

        analysis["landmarks"] = landmark_coords
        analysis["landmarks_detected"] = True
        
        return analysis
