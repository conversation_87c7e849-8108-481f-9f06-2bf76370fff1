import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WebcamCapture } from "./components/WebcamCapture";
import { VideoUpload } from "./components/VideoUpload";
import { PostureResults } from "./components/PostureResults";
import "./index.css";

type PostureType = "squat" | "sitting" | "auto";
type AnalysisMode = "webcam" | "upload";

interface PostureAnalysis {
  posture_type: string;
  issues: string[];
  is_good_posture: boolean;
  knee_angle?: number;
  back_angle?: number;
  neck_angle?: number;
  landmarks_detected: boolean;
  error?: string;
}

interface VideoAnalysis {
  total_frames_analyzed: number;
  good_posture_frames: number;
  bad_posture_frames: number;
  posture_score: number;
  frame_analyses: PostureAnalysis[];
}

export function App() {
  const [mode, setMode] = useState<AnalysisMode>("webcam");
  const [postureType, setPostureType] = useState<PostureType>("auto");
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<
    PostureAnalysis | VideoAnalysis | null
  >(null);
  const [isVideoAnalysis, setIsVideoAnalysis] = useState(false);

  const API_BASE_URL = process.env.REACT_APP_API_URL || "http://localhost:3001";

  const handleFrameCapture = async (imageData: string) => {
    if (!isAnalyzing) return;

    try {
      const response = await fetch(`${API_BASE_URL}/api/analyze-frame`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image_data: imageData,
          posture_type: postureType,
        }),
      });

      const result = await response.json();
      setCurrentAnalysis(result);
      setIsVideoAnalysis(false);
    } catch (error) {
      console.error("Error analyzing frame:", error);
      setCurrentAnalysis({
        posture_type: "unknown",
        issues: ["Failed to connect to analysis service"],
        is_good_posture: false,
        landmarks_detected: false,
        error: "Connection error",
      });
    }
  };

  const handleVideoUpload = async (file: File) => {
    setIsAnalyzing(true);
    setCurrentAnalysis(null);

    try {
      const formData = new FormData();
      formData.append("video", file);
      formData.append("posture_type", postureType);

      const response = await fetch(`${API_BASE_URL}/api/analyze-video`, {
        method: "POST",
        body: formData,
      });

      const result = await response.json();
      setCurrentAnalysis(result);
      setIsVideoAnalysis(true);
    } catch (error) {
      console.error("Error analyzing video:", error);
      setCurrentAnalysis({
        posture_type: "unknown",
        issues: ["Failed to upload and analyze video"],
        is_good_posture: false,
        landmarks_detected: false,
        error: "Upload error",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const toggleAnalysis = () => {
    setIsAnalyzing(!isAnalyzing);
    if (isAnalyzing) {
      setCurrentAnalysis(null);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="container mx-auto max-w-6xl space-y-6">
        {/* Header */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="text-4xl font-bold text-gray-800">
              🏃‍♂️ Posture Detection App
            </CardTitle>
            <p className="text-lg text-gray-600">
              AI-powered posture analysis for squats and desk sitting
            </p>
          </CardHeader>
        </Card>

        {/* Controls */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-4 items-center justify-center">
              <div className="space-y-2">
                <label className="text-sm font-medium">Analysis Mode</label>
                <div className="flex gap-2">
                  <Button
                    variant={mode === "webcam" ? "default" : "outline"}
                    onClick={() => setMode("webcam")}
                    className="w-32"
                  >
                    📹 Webcam
                  </Button>
                  <Button
                    variant={mode === "upload" ? "default" : "outline"}
                    onClick={() => setMode("upload")}
                    className="w-32"
                  >
                    📁 Upload
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Posture Type</label>
                <Select
                  value={postureType}
                  onValueChange={(value: PostureType) => setPostureType(value)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto Detect</SelectItem>
                    <SelectItem value="squat">Squat</SelectItem>
                    <SelectItem value="sitting">Sitting</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {mode === "webcam" && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Real-time Analysis
                  </label>
                  <Button
                    onClick={toggleAnalysis}
                    variant={isAnalyzing ? "destructive" : "default"}
                    className="w-40"
                  >
                    {isAnalyzing ? "Stop Analysis" : "Start Analysis"}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            {mode === "webcam" ? (
              <WebcamCapture
                onFrameCapture={handleFrameCapture}
                isAnalyzing={isAnalyzing}
                postureType={postureType}
              />
            ) : (
              <VideoUpload
                onVideoUpload={handleVideoUpload}
                isAnalyzing={isAnalyzing}
                postureType={postureType}
              />
            )}
          </div>

          <div className="space-y-6">
            {currentAnalysis && (
              <PostureResults
                analysis={currentAnalysis}
                isVideo={isVideoAnalysis}
              />
            )}

            {!currentAnalysis && (
              <Card className="h-full flex items-center justify-center">
                <CardContent className="text-center py-12">
                  <div className="text-6xl mb-4">🤖</div>
                  <h3 className="text-xl font-semibold mb-2">
                    Ready for Analysis
                  </h3>
                  <p className="text-muted-foreground">
                    {mode === "webcam"
                      ? "Start your camera and begin real-time posture analysis"
                      : "Upload a video file to analyze posture throughout the recording"}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">🏋️‍♀️ For Squat Analysis:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Position yourself sideways to the camera</li>
                  <li>• Ensure your full body is visible</li>
                  <li>• Perform squats with normal form</li>
                  <li>
                    • The app will detect knee-over-toe and back posture issues
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">💺 For Sitting Analysis:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Sit facing the camera</li>
                  <li>• Ensure your upper body is clearly visible</li>
                  <li>• Maintain your normal sitting posture</li>
                  <li>• The app will detect neck forward head and slouching</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default App;
