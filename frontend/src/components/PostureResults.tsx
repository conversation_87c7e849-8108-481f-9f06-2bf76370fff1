import React from 'react';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from '@/components/ui/card';

interface PostureIssue {
  issue: string;
  severity?: 'low' | 'medium' | 'high';
}

interface PostureAnalysis {
  posture_type: string;
  issues: string[];
  is_good_posture: boolean;
  knee_angle?: number;
  back_angle?: number;
  neck_angle?: number;
  landmarks_detected: boolean;
  error?: string;
}

interface VideoAnalysis {
  total_frames_analyzed: number;
  good_posture_frames: number;
  bad_posture_frames: number;
  posture_score: number;
  frame_analyses: PostureAnalysis[];
}

interface PostureResultsProps {
  analysis: PostureAnalysis | VideoAnalysis | null;
  isVideo?: boolean;
}

export const PostureResults: React.FC<PostureResultsProps> = ({
  analysis,
  isVideo = false
}) => {
  if (!analysis) return null;

  if ('error' in analysis && analysis.error) {
    return (
      <Card className="w-full max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="text-destructive">Analysis Error</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">{analysis.error}</p>
        </CardContent>
      </Card>
    );
  }

  const renderFrameAnalysis = (frameAnalysis: PostureAnalysis) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          {frameAnalysis.posture_type === 'squat' ? 'Squat Analysis' : 'Sitting Posture Analysis'}
        </h3>
        <div className={`px-3 py-1 rounded-full text-sm font-medium ${
          frameAnalysis.is_good_posture 
            ? 'bg-green-100 text-green-800' 
            : 'bg-red-100 text-red-800'
        }`}>
          {frameAnalysis.is_good_posture ? '✓ Good Posture' : '⚠ Poor Posture'}
        </div>
      </div>

      {frameAnalysis.issues.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-destructive">Issues Detected:</h4>
          <ul className="space-y-1">
            {frameAnalysis.issues.map((issue, index) => (
              <li key={index} className="flex items-start gap-2 text-sm">
                <span className="text-destructive mt-0.5">•</span>
                <span>{issue}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      <div className="grid grid-cols-2 gap-4 text-sm">
        {frameAnalysis.knee_angle && (
          <div className="space-y-1">
            <p className="font-medium">Knee Angle</p>
            <p className="text-muted-foreground">{frameAnalysis.knee_angle.toFixed(1)}°</p>
          </div>
        )}
        {frameAnalysis.back_angle && (
          <div className="space-y-1">
            <p className="font-medium">Back Angle</p>
            <p className="text-muted-foreground">{frameAnalysis.back_angle.toFixed(1)}°</p>
          </div>
        )}
        {frameAnalysis.neck_angle && (
          <div className="space-y-1">
            <p className="font-medium">Neck Angle</p>
            <p className="text-muted-foreground">{frameAnalysis.neck_angle.toFixed(1)}°</p>
          </div>
        )}
        <div className="space-y-1">
          <p className="font-medium">Pose Detection</p>
          <p className="text-muted-foreground">
            {frameAnalysis.landmarks_detected ? 'Successful' : 'Failed'}
          </p>
        </div>
      </div>
    </div>
  );

  const renderVideoAnalysis = (videoAnalysis: VideoAnalysis) => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">Video Analysis Complete</h3>
        <div className="text-4xl font-bold mb-2">
          <span className={videoAnalysis.posture_score >= 70 ? 'text-green-600' : 
                          videoAnalysis.posture_score >= 50 ? 'text-yellow-600' : 'text-red-600'}>
            {videoAnalysis.posture_score.toFixed(1)}%
          </span>
        </div>
        <p className="text-muted-foreground">Overall Posture Score</p>
      </div>

      <div className="grid grid-cols-3 gap-4 text-center">
        <div className="space-y-1">
          <p className="text-2xl font-bold text-blue-600">{videoAnalysis.total_frames_analyzed}</p>
          <p className="text-sm text-muted-foreground">Frames Analyzed</p>
        </div>
        <div className="space-y-1">
          <p className="text-2xl font-bold text-green-600">{videoAnalysis.good_posture_frames}</p>
          <p className="text-sm text-muted-foreground">Good Posture</p>
        </div>
        <div className="space-y-1">
          <p className="text-2xl font-bold text-red-600">{videoAnalysis.bad_posture_frames}</p>
          <p className="text-sm text-muted-foreground">Poor Posture</p>
        </div>
      </div>

      <div className="w-full bg-gray-200 rounded-full h-3">
        <div 
          className="bg-green-600 h-3 rounded-full transition-all duration-500"
          style={{ width: `${videoAnalysis.posture_score}%` }}
        ></div>
      </div>

      {videoAnalysis.frame_analyses.length > 0 && (
        <div className="space-y-4">
          <h4 className="font-semibold">Sample Frame Analysis:</h4>
          {renderFrameAnalysis(videoAnalysis.frame_analyses[0])}
        </div>
      )}

      <div className="text-sm text-muted-foreground">
        <p>
          <strong>Note:</strong> Analysis was performed on every 5th frame to optimize processing time.
          Results represent a sample of your posture throughout the video.
        </p>
      </div>
    </div>
  );

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>
          {isVideo ? 'Video Analysis Results' : 'Real-time Posture Analysis'}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isVideo ? 
          renderVideoAnalysis(analysis as VideoAnalysis) : 
          renderFrameAnalysis(analysis as PostureAnalysis)
        }
      </CardContent>
    </Card>
  );
};
