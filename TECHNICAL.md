# 🔧 Technical Documentation

## 🏗️ Architecture Overview

The Posture Detection App follows a modern full-stack architecture with clear separation of concerns:

```
┌─────────────────────────────────────────────────────────────┐
│                    Frontend (React)                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Webcam    │ │   Video     │ │     Results Display     │ │
│  │  Component  │ │   Upload    │ │      Component          │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │ HTTP/REST API
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                   Backend (Express)                        │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Express   │ │   Multer    │ │    Pose Detection       │ │
│  │   Routes    │ │   Upload    │ │      Service            │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                 TensorFlow.js + MoveNet                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────────────────┐ │
│  │   Pose      │ │   Keypoint  │ │    Rule-based           │ │
│  │ Detection   │ │ Extraction  │ │     Analysis            │ │
│  └─────────────┘ └─────────────┘ └─────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🧠 Pose Detection Pipeline

### 1. Image Processing
```typescript
// Convert base64 to tensor
const imageTensor = tf.node.decodeImage(imageBuffer, 3);

// Run pose detection
const poses = await this.detector.estimatePoses(imageTensor);
```

### 2. Keypoint Extraction
The MoveNet model detects 17 key body landmarks:
- Head: nose, left_eye, right_eye, left_ear, right_ear
- Torso: left_shoulder, right_shoulder, left_hip, right_hip
- Arms: left_elbow, right_elbow, left_wrist, right_wrist
- Legs: left_knee, right_knee, left_ankle, right_ankle

### 3. Rule-Based Analysis

#### Squat Analysis Rules
```typescript
// Knee-over-toe check
if (leftKnee.x < leftAnkle.x - 0.05) {
  issues.push('Left knee is going too far forward over toe');
}

// Back angle check
const backAngle = calculateAngle(shoulder, hip, knee);
if (backAngle < 150) {
  issues.push('Back is too hunched forward');
}
```

#### Sitting Analysis Rules
```typescript
// Neck forward head posture
const neckAngle = Math.atan2(nose.x - shoulder.x, shoulder.y - nose.y) * 180 / Math.PI;
if (neckAngle > 30) {
  issues.push('Neck is bent too far forward');
}

// Back straightness
const backAngle = Math.atan2(shoulder.x - hip.x, hip.y - shoulder.y) * 180 / Math.PI;
if (backAngle > 20) {
  issues.push('Back is not straight');
}
```

## 📊 Data Flow

### Real-time Analysis Flow
1. **Webcam Capture** → Canvas → Base64 encoding
2. **Frontend** → HTTP POST to `/api/analyze-frame`
3. **Backend** → Decode image → TensorFlow.js processing
4. **Pose Detection** → Keypoint extraction → Rule analysis
5. **Response** → JSON with analysis results
6. **Frontend** → Display results in real-time

### Video Analysis Flow
1. **File Upload** → Multer middleware → Temporary storage
2. **Backend** → File processing (currently mock implementation)
3. **Frame Extraction** → Multiple frame analysis
4. **Aggregation** → Summary statistics and scoring
5. **Response** → Comprehensive analysis report
6. **Cleanup** → Remove temporary files

## 🔧 Key Components

### Frontend Components

#### WebcamCapture.tsx
- Manages webcam access and streaming
- Captures frames at 500ms intervals during analysis
- Handles browser permissions and error states

#### VideoUpload.tsx
- File upload with validation (type, size)
- Video preview functionality
- Progress indication during analysis

#### PostureResults.tsx
- Displays analysis results with visual indicators
- Handles both real-time and video analysis results
- Responsive design for different screen sizes

### Backend Services

#### poseDetection.ts
- TensorFlow.js model initialization
- Pose detection and keypoint extraction
- Rule-based posture analysis algorithms

#### index.ts
- Express server setup and middleware
- API route definitions
- File upload handling with Multer

## 🎯 Performance Considerations

### Model Optimization
- **MoveNet Lightning**: Chosen for speed over accuracy
- **Single Pose**: Optimized for individual analysis
- **Client-side Caching**: Model loaded once on startup

### Memory Management
```typescript
// Proper tensor disposal
const imageTensor = tf.node.decodeImage(imageBuffer, 3);
const poses = await this.detector.estimatePoses(imageTensor);
imageTensor.dispose(); // Prevent memory leaks
```

### API Optimization
- **Frame Rate Limiting**: 500ms intervals for real-time analysis
- **File Size Limits**: 100MB max for video uploads
- **Compression**: JPEG encoding for frame capture

## 🔒 Security Considerations

### Input Validation
- File type validation for uploads
- Image format verification
- Size limits to prevent DoS attacks

### Data Privacy
- No persistent storage of images/videos
- Temporary files cleaned up after processing
- Client-side processing where possible

## 🧪 Testing Strategy

### Unit Tests
```typescript
// Example test structure
describe('PostureAnalyzer', () => {
  test('should detect knee-over-toe in squat', () => {
    // Test implementation
  });
  
  test('should identify forward head posture', () => {
    // Test implementation
  });
});
```

### Integration Tests
- API endpoint testing
- File upload validation
- Error handling verification

### E2E Tests
- Webcam functionality
- Complete analysis workflow
- Cross-browser compatibility

## 📈 Monitoring and Logging

### Backend Logging
```typescript
console.log('Pose detector initialized successfully');
console.error('Error analyzing frame:', error);
```

### Performance Metrics
- Model initialization time
- Frame processing latency
- Memory usage monitoring

## 🔄 Future Enhancements

### Technical Improvements
1. **Video Frame Extraction**: Implement FFmpeg for proper video processing
2. **Model Upgrades**: Consider PoseNet or BlazePose for better accuracy
3. **Caching**: Redis for session management and result caching
4. **WebRTC**: Direct peer-to-peer video streaming

### Feature Additions
1. **Multiple Person Detection**: Analyze groups or compare poses
2. **Historical Tracking**: Store and track posture improvements
3. **Custom Rules**: User-defined posture criteria
4. **Mobile App**: React Native implementation

### Scalability
1. **Microservices**: Separate pose detection service
2. **Load Balancing**: Multiple backend instances
3. **CDN**: Global content delivery for models
4. **Database**: Persistent storage for user data

---

This technical documentation provides the foundation for understanding, maintaining, and extending the Posture Detection App.
