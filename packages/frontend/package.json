{"name": "@posture-app/frontend", "version": "1.0.0", "description": "Frontend React application for posture detection", "private": true, "type": "module", "main": "src/index.tsx", "scripts": {"dev": "bun --hot src/index.tsx", "start": "NODE_ENV=production bun src/index.tsx", "build": "bun run build.ts", "preview": "bun run build && bun run start", "lint": "eslint src/**/*.{ts,tsx}", "lint:fix": "eslint src/**/*.{ts,tsx} --fix", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@hookform/resolvers": "^4.1.0", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.2", "bun-plugin-tailwind": "^0.0.14", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.475.0", "react": "^19", "react-dom": "^19", "react-hook-form": "^7.54.2", "tailwind-merge": "^3.0.1", "tailwindcss": "^4.0.6", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@types/react": "^19", "@types/react-dom": "^19", "@types/bun": "latest", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "eslint-plugin-react": "^7.33.0", "eslint-plugin-react-hooks": "^4.6.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}