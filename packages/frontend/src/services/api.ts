// API service for communicating with the backend

export interface PostureAnalysis {
  posture_type: 'squat' | 'sitting' | 'unknown';
  issues: string[];
  is_good_posture: boolean;
  knee_angle?: number;
  back_angle?: number;
  neck_angle?: number;
  landmarks_detected: boolean;
  landmarks?: any[];
  confidence_score?: number;
  timestamp: string;
  error?: string;
}

export interface VideoAnalysis {
  total_frames_analyzed: number;
  good_posture_frames: number;
  bad_posture_frames: number;
  posture_score: number;
  frame_analyses: (PostureAnalysis & { frame_number: number })[];
  video_duration?: number;
  analysis_duration?: number;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  meta?: {
    processing_time_ms?: number;
    timestamp?: string;
    note?: string;
  };
}

class ApiService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = this.getApiBaseUrl();
  }

  private getApiBaseUrl(): string {
    // Check for environment variable first
    if (typeof process !== 'undefined' && process.env?.REACT_APP_API_URL) {
      return process.env.REACT_APP_API_URL;
    }
    
    // Check for window environment variable (for runtime config)
    if (typeof window !== 'undefined' && (window as any).ENV?.API_URL) {
      return (window as any).ENV.API_URL;
    }
    
    // Default to localhost for development
    return 'http://localhost:3001';
  }

  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseUrl}${endpoint}`;
      
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers,
        },
        ...options,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async analyzeFrame(
    imageData: string,
    postureType: 'squat' | 'sitting' | 'auto' = 'auto'
  ): Promise<ApiResponse<PostureAnalysis>> {
    return this.makeRequest<PostureAnalysis>('/api/analyze-frame', {
      method: 'POST',
      body: JSON.stringify({
        image_data: imageData,
        posture_type: postureType,
      }),
    });
  }

  async analyzeVideo(
    file: File,
    postureType: 'squat' | 'sitting' | 'auto' = 'auto'
  ): Promise<ApiResponse<VideoAnalysis>> {
    try {
      const formData = new FormData();
      formData.append('video', file);
      formData.append('posture_type', postureType);

      const url = `${this.baseUrl}/api/analyze-video`;
      
      const response = await fetch(url, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Video analysis failed:', error);
      
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  async getHealthCheck(): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('/health');
  }

  async getApiStatus(): Promise<ApiResponse<any>> {
    return this.makeRequest<any>('/api/status');
  }

  // Utility method to check if API is reachable
  async checkConnection(): Promise<boolean> {
    try {
      const response = await this.getHealthCheck();
      return response.success;
    } catch {
      return false;
    }
  }
}

// Export singleton instance
export const apiService = new ApiService();

// Export class for testing
export { ApiService };
