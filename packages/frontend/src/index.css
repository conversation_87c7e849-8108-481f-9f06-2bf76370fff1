@import "../styles/globals.css";

@layer base {
  :root {
    @apply font-sans;
  }

  body {
    @apply grid place-items-center min-w-[320px] min-h-screen relative m-0 bg-background text-foreground;
  }
}

/* cool Bun background animation 😎 */
body::before {
  content: "";
  position: fixed;
  inset: 0;
  z-index: -1;
  opacity: 0.05;
  background: url("./logo.svg");
  background-size: 256px;
  transform: rotate(-12deg) scale(1.35);
  animation: slide 30s linear infinite;
  pointer-events: none;
}

@keyframes slide {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 256px 224px;
  }
}

@keyframes spin {
  from {
    transform: rotate(0);
  }
  to {
    transform: rotate(360deg);
  }
}

@media (prefers-reduced-motion) {
  *,
  ::before,
  ::after {
    animation: none !important;
  }
}
