import React, { useRef, useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface VideoUploadProps {
  onVideoUpload: (file: File) => void;
  isAnalyzing: boolean;
  postureType: 'squat' | 'sitting' | 'auto';
}

export const VideoUpload: React.FC<VideoUploadProps> = ({
  onVideoUpload,
  isAnalyzing,
  postureType
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [videoUrl, setVideoUrl] = useState<string>('');
  const [error, setError] = useState<string>('');

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file type
    if (!file.type.startsWith('video/')) {
      setError('Please select a valid video file.');
      return;
    }

    // Validate file size (max 100MB)
    if (file.size > 100 * 1024 * 1024) {
      setError('File size must be less than 100MB.');
      return;
    }

    setSelectedFile(file);
    setError('');

    // Create preview URL
    const url = URL.createObjectURL(file);
    setVideoUrl(url);
  };

  const handleUpload = () => {
    if (selectedFile) {
      onVideoUpload(selectedFile);
    }
  };

  const clearSelection = () => {
    setSelectedFile(null);
    if (videoUrl) {
      URL.revokeObjectURL(videoUrl);
      setVideoUrl('');
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    setError('');
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Video Upload Analysis</CardTitle>
        <p className="text-sm text-muted-foreground">
          Upload a video file to analyze posture throughout the entire video.
          Current mode: <span className="font-semibold">{postureType}</span>
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="video-upload">Select Video File</Label>
          <Input
            id="video-upload"
            ref={fileInputRef}
            type="file"
            accept="video/*"
            onChange={handleFileSelect}
            disabled={isAnalyzing}
          />
          <p className="text-xs text-muted-foreground">
            Supported formats: MP4, WebM, AVI, MOV (max 100MB)
          </p>
        </div>

        {selectedFile && (
          <div className="space-y-3">
            <div className="p-3 bg-muted rounded-md">
              <div className="flex justify-between items-start">
                <div>
                  <p className="font-medium text-sm">{selectedFile.name}</p>
                  <p className="text-xs text-muted-foreground">
                    Size: {formatFileSize(selectedFile.size)}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Type: {selectedFile.type}
                  </p>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearSelection}
                  disabled={isAnalyzing}
                >
                  ✕
                </Button>
              </div>
            </div>

            {videoUrl && (
              <div className="relative">
                <video
                  ref={videoRef}
                  src={videoUrl}
                  controls
                  className="w-full h-auto rounded-lg border"
                  style={{ maxHeight: '300px' }}
                />
              </div>
            )}

            <div className="flex gap-2 justify-center">
              <Button
                onClick={handleUpload}
                disabled={isAnalyzing}
                className="w-40"
              >
                {isAnalyzing ? 'Analyzing...' : 'Analyze Video'}
              </Button>
              <Button
                variant="outline"
                onClick={clearSelection}
                disabled={isAnalyzing}
                className="w-32"
              >
                Clear
              </Button>
            </div>
          </div>
        )}

        {!selectedFile && (
          <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-8 text-center">
            <div className="space-y-2">
              <div className="text-4xl">📹</div>
              <p className="text-sm text-muted-foreground">
                Click "Select Video File" above or drag and drop a video file here
              </p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
