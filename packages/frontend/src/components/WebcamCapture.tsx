import React, { useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useWebcam } from "../hooks/useWebcam";
import { PostureType } from "../hooks/usePostureAnalysis";

interface WebcamCaptureProps {
  onFrameCapture: (imageData: string) => void;
  isAnalyzing: boolean;
  postureType: PostureType;
}

export const WebcamCapture: React.FC<WebcamCaptureProps> = ({
  onFrameCapture,
  isAnalyzing,
  postureType,
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [error, setError] = useState<string>("");
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const startWebcam = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 640 },
          height: { ideal: 480 },
          facingMode: "user",
        },
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        setIsStreaming(true);
        setError("");
      }
    } catch (err) {
      setError(
        "Failed to access webcam. Please ensure you have granted camera permissions."
      );
      console.error("Error accessing webcam:", err);
    }
  }, []);

  const stopWebcam = useCallback(() => {
    if (videoRef.current && videoRef.current.srcObject) {
      const stream = videoRef.current.srcObject as MediaStream;
      stream.getTracks().forEach((track) => track.stop());
      videoRef.current.srcObject = null;
    }
    setIsStreaming(false);

    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  const captureFrame = useCallback(() => {
    if (!videoRef.current || !canvasRef.current || !isStreaming) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const ctx = canvas.getContext("2d");

    if (!ctx) return;

    // Set canvas dimensions to match video
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;

    // Draw current video frame to canvas
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert to base64
    const imageData = canvas.toDataURL("image/jpeg", 0.8);
    onFrameCapture(imageData);
  }, [isStreaming, onFrameCapture]);

  const startAnalysis = useCallback(() => {
    if (!isStreaming) return;

    // Capture frames every 500ms for real-time analysis
    intervalRef.current = setInterval(captureFrame, 500);
  }, [isStreaming, captureFrame]);

  const stopAnalysis = useCallback(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
      intervalRef.current = null;
    }
  }, []);

  useEffect(() => {
    if (isAnalyzing && isStreaming) {
      startAnalysis();
    } else {
      stopAnalysis();
    }

    return () => stopAnalysis();
  }, [isAnalyzing, isStreaming, startAnalysis, stopAnalysis]);

  useEffect(() => {
    return () => {
      stopWebcam();
    };
  }, [stopWebcam]);

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Live Webcam Analysis</CardTitle>
        <p className="text-sm text-muted-foreground">
          Position yourself in front of the camera and start analysis for
          real-time posture feedback. Current mode:{" "}
          <span className="font-semibold">{postureType}</span>
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        {error && (
          <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-md">
            <p className="text-sm text-destructive">{error}</p>
          </div>
        )}

        <div className="relative">
          <video
            ref={videoRef}
            autoPlay
            playsInline
            muted
            className="w-full h-auto rounded-lg border"
            style={{ maxHeight: "400px" }}
          />
          <canvas ref={canvasRef} className="hidden" />

          {isStreaming && isAnalyzing && (
            <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs">
              ● ANALYZING
            </div>
          )}
        </div>

        <div className="flex gap-2 justify-center">
          {!isStreaming ? (
            <Button onClick={startWebcam} className="w-32">
              Start Camera
            </Button>
          ) : (
            <Button onClick={stopWebcam} variant="outline" className="w-32">
              Stop Camera
            </Button>
          )}

          {isStreaming && (
            <Button onClick={captureFrame} variant="secondary" className="w-32">
              Capture Frame
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
