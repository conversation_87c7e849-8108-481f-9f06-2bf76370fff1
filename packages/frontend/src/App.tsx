import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WebcamCapture } from "./components/WebcamCapture";
import { VideoUpload } from "./components/VideoUpload";
import { PostureResults } from "./components/PostureResults";
import { usePostureAnalysis, PostureType } from "./hooks/usePostureAnalysis";
import "./index.css";

type AnalysisMode = "webcam" | "upload";

export function App() {
  const [mode, setMode] = useState<AnalysisMode>("webcam");
  const [postureType, setPostureType] = useState<PostureType>("auto");
  const [isRealTimeAnalysis, setIsRealTimeAnalysis] = useState(false);

  const {
    isAnalyzing,
    currentAnalysis,
    isVideoAnalysis,
    error: analysisError,
    analyzeFrame,
    analyzeVideo,
    clearAnalysis,
    clearError,
  } = usePostureAnalysis();

  const handleFrameCapture = async (imageData: string) => {
    if (!isRealTimeAnalysis) return;
    await analyzeFrame(imageData, postureType);
  };

  const handleVideoUpload = async (file: File) => {
    await analyzeVideo(file, postureType);
  };

  const toggleAnalysis = () => {
    setIsRealTimeAnalysis(!isRealTimeAnalysis);
    if (isRealTimeAnalysis) {
      clearAnalysis();
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="container mx-auto max-w-6xl space-y-6">
        {/* Header */}
        <Card className="text-center">
          <CardHeader>
            <CardTitle className="text-4xl font-bold text-gray-800">
              🏃‍♂️ Posture Detection App
            </CardTitle>
            <p className="text-lg text-gray-600">
              AI-powered posture analysis for squats and desk sitting
            </p>
          </CardHeader>
        </Card>

        {/* Controls */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-wrap gap-4 items-center justify-center">
              <div className="space-y-2">
                <label className="text-sm font-medium">Analysis Mode</label>
                <div className="flex gap-2">
                  <Button
                    variant={mode === "webcam" ? "default" : "outline"}
                    onClick={() => setMode("webcam")}
                    className="w-32"
                  >
                    📹 Webcam
                  </Button>
                  <Button
                    variant={mode === "upload" ? "default" : "outline"}
                    onClick={() => setMode("upload")}
                    className="w-32"
                  >
                    📁 Upload
                  </Button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Posture Type</label>
                <Select
                  value={postureType}
                  onValueChange={(value: PostureType) => setPostureType(value)}
                >
                  <SelectTrigger className="w-40">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="auto">Auto Detect</SelectItem>
                    <SelectItem value="squat">Squat</SelectItem>
                    <SelectItem value="sitting">Sitting</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {mode === "webcam" && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">
                    Real-time Analysis
                  </label>
                  <Button
                    onClick={toggleAnalysis}
                    variant={isAnalyzing ? "destructive" : "default"}
                    className="w-40"
                  >
                    {isAnalyzing ? "Stop Analysis" : "Start Analysis"}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content */}
        <div className="grid lg:grid-cols-2 gap-6">
          <div className="space-y-6">
            {mode === "webcam" ? (
              <WebcamCapture
                onFrameCapture={handleFrameCapture}
                isAnalyzing={isAnalyzing}
                postureType={postureType}
              />
            ) : (
              <VideoUpload
                onVideoUpload={handleVideoUpload}
                isAnalyzing={isAnalyzing}
                postureType={postureType}
              />
            )}
          </div>

          <div className="space-y-6">
            {currentAnalysis && (
              <PostureResults
                analysis={currentAnalysis}
                isVideo={isVideoAnalysis}
              />
            )}

            {!currentAnalysis && (
              <Card className="h-full flex items-center justify-center">
                <CardContent className="text-center py-12">
                  <div className="text-6xl mb-4">🤖</div>
                  <h3 className="text-xl font-semibold mb-2">
                    Ready for Analysis
                  </h3>
                  <p className="text-muted-foreground">
                    {mode === "webcam"
                      ? "Start your camera and begin real-time posture analysis"
                      : "Upload a video file to analyze posture throughout the recording"}
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>How to Use</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-semibold mb-2">🏋️‍♀️ For Squat Analysis:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Position yourself sideways to the camera</li>
                  <li>• Ensure your full body is visible</li>
                  <li>• Perform squats with normal form</li>
                  <li>
                    • The app will detect knee-over-toe and back posture issues
                  </li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">💺 For Sitting Analysis:</h4>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Sit facing the camera</li>
                  <li>• Ensure your upper body is clearly visible</li>
                  <li>• Maintain your normal sitting posture</li>
                  <li>• The app will detect neck forward head and slouching</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default App;
