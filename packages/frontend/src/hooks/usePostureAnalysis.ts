import { useState, useCallback } from 'react';
import { apiService, PostureAnalysis, VideoAnalysis } from '../services/api';

export type PostureType = 'squat' | 'sitting' | 'auto';

interface UsePostureAnalysisReturn {
  // State
  isAnalyzing: boolean;
  currentAnalysis: PostureAnalysis | VideoAnalysis | null;
  isVideoAnalysis: boolean;
  error: string | null;
  
  // Actions
  analyzeFrame: (imageData: string, postureType?: PostureType) => Promise<void>;
  analyzeVideo: (file: File, postureType?: PostureType) => Promise<void>;
  clearAnalysis: () => void;
  clearError: () => void;
}

export const usePostureAnalysis = (): UsePostureAnalysisReturn => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [currentAnalysis, setCurrentAnalysis] = useState<PostureAnalysis | VideoAnalysis | null>(null);
  const [isVideoAnalysis, setIsVideoAnalysis] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const analyzeFrame = useCallback(async (
    imageData: string,
    postureType: PostureType = 'auto'
  ) => {
    setIsAnalyzing(true);
    setError(null);
    
    try {
      const response = await apiService.analyzeFrame(imageData, postureType);
      
      if (response.success && response.data) {
        setCurrentAnalysis(response.data);
        setIsVideoAnalysis(false);
      } else {
        setError(response.error || 'Failed to analyze frame');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const analyzeVideo = useCallback(async (
    file: File,
    postureType: PostureType = 'auto'
  ) => {
    setIsAnalyzing(true);
    setError(null);
    setCurrentAnalysis(null);
    
    try {
      const response = await apiService.analyzeVideo(file, postureType);
      
      if (response.success && response.data) {
        setCurrentAnalysis(response.data);
        setIsVideoAnalysis(true);
      } else {
        setError(response.error || 'Failed to analyze video');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred');
    } finally {
      setIsAnalyzing(false);
    }
  }, []);

  const clearAnalysis = useCallback(() => {
    setCurrentAnalysis(null);
    setIsVideoAnalysis(false);
    setError(null);
  }, []);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    isAnalyzing,
    currentAnalysis,
    isVideoAnalysis,
    error,
    analyzeFrame,
    analyzeVideo,
    clearAnalysis,
    clearError,
  };
};
