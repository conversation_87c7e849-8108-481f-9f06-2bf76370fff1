{"error": "File type video/x-matroska not allowed. Allowed types: video/mp4, video/webm, video/avi, video/mov, video/quicktime", "ip": "::1", "level": "error", "message": "Error occurred:", "method": "POST", "service": "posture-detection-api", "stack": "Error: File type video/x-matroska not allowed. Allowed types: video/mp4, video/webm, video/avi, video/mov, video/quicktime\n    at fileFilter (/home/<USER>/assignments/Realfy_Oasis/packages/backend/src/middleware/uploadMiddleware.ts:38:19)\n    at wrappedFileFilter (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/multer/index.js:44:7)\n    at <anonymous> (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/multer/lib/make-middleware.js:123:7)\n    at emit (node:events:101:22)\n    at <anonymous> (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/busboy/lib/types/multipart.js:358:14)\n    at push (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/busboy/lib/types/multipart.js:162:20)\n    at ssCb (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/busboy/lib/types/multipart.js:394:37)\n    at feed (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/streamsearch/lib/sbmh.js:248:10)\n    at push (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/streamsearch/lib/sbmh.js:104:16)\n    at _write (/home/<USER>/assignments/Realfy_Oasis/packages/backend/node_modules/busboy/lib/types/multipart.js:567:19)\n    at writeOrBuffer (internal:streams/writable:283:70)\n    at <anonymous> (internal:streams/writable:249:16)\n    at ondata (internal:streams/readable:456:19)\n    at emit (node:events:95:22)\n    at addChunk (internal:streams/readable:265:47)\n    at readableAddChunkPushByteMode (internal:streams/readable:243:18)\n    at onDataIncomingMessage (node:_http_incoming:96:14)", "statusCode": 400, "timestamp": "2025-07-08T16:06:45.960Z", "url": "/api/analyze-video", "userAgent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}