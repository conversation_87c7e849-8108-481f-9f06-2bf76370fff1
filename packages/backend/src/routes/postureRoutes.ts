import { Router } from 'express';
import { PostureController } from '../controllers/PostureController';
import { PostureService } from '../services/PostureService';
import { uploadMiddleware } from '../middleware/uploadMiddleware';

export const postureRoutes = (postureService: PostureService): Router => {
  const router = Router();
  const postureController = new PostureController(postureService);

  // API documentation endpoint
  router.get('/', (req, res) => {
    res.json({
      name: 'Posture Detection API',
      version: '1.0.0',
      endpoints: {
        'POST /api/analyze-frame': 'Analyze a single image frame for posture',
        'POST /api/analyze-video': 'Analyze an uploaded video for posture',
        'GET /api/status': 'Get API status and model information'
      },
      documentation: 'https://github.com/your-repo/posture-detection-app'
    });
  });

  // Analyze single frame
  router.post('/analyze-frame', postureController.analyzeFrame.bind(postureController));

  // Analyze video
  router.post('/analyze-video', 
    uploadMiddleware.single('video'), 
    postureController.analyzeVideo.bind(postureController)
  );

  // Get API status
  router.get('/status', postureController.getStatus.bind(postureController));

  return router;
};
