import multer from 'multer';
import path from 'path';
import fs from 'fs';
import { Request } from 'express';
import { config } from '../config';
import { CustomError } from './errorHandler';
import { logger } from '../utils/logger';

// Ensure upload directory exists
if (!fs.existsSync(config.upload.uploadDir)) {
  fs.mkdirSync(config.upload.uploadDir, { recursive: true });
}

// Configure storage
const storage = multer.diskStorage({
  destination: (req: Request, file: Express.Multer.File, cb) => {
    cb(null, config.upload.uploadDir);
  },
  filename: (req: Request, file: Express.Multer.File, cb) => {
    // Generate unique filename with timestamp
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const extension = path.extname(file.originalname);
    const filename = `${file.fieldname}-${uniqueSuffix}${extension}`;
    cb(null, filename);
  }
});

// File filter function
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Check file type
  if (!config.upload.allowedTypes.includes(file.mimetype)) {
    logger.warn(`Rejected file upload: ${file.mimetype} not in allowed types`, {
      filename: file.originalname,
      mimetype: file.mimetype,
      allowedTypes: config.upload.allowedTypes
    });
    
    return cb(new CustomError(
      `File type ${file.mimetype} not allowed. Allowed types: ${config.upload.allowedTypes.join(', ')}`,
      400
    ));
  }

  // Check file extension
  const allowedExtensions = ['.mp4', '.webm', '.avi', '.mov', '.quicktime'];
  const fileExtension = path.extname(file.originalname).toLowerCase();
  
  if (!allowedExtensions.includes(fileExtension)) {
    logger.warn(`Rejected file upload: ${fileExtension} extension not allowed`, {
      filename: file.originalname,
      extension: fileExtension,
      allowedExtensions
    });
    
    return cb(new CustomError(
      `File extension ${fileExtension} not allowed. Allowed extensions: ${allowedExtensions.join(', ')}`,
      400
    ));
  }

  logger.debug('File upload accepted', {
    filename: file.originalname,
    mimetype: file.mimetype,
    size: file.size
  });

  cb(null, true);
};

// Configure multer
export const uploadMiddleware = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB
    files: 1, // Only allow 1 file per request
  },
});

// Cleanup function to remove uploaded files
export const cleanupFile = (filePath: string): void => {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
      logger.debug(`Cleaned up file: ${filePath}`);
    }
  } catch (error) {
    logger.error(`Failed to cleanup file ${filePath}:`, error);
  }
};

// Cleanup old files (older than 1 hour)
export const cleanupOldFiles = (): void => {
  try {
    const uploadDir = config.upload.uploadDir;
    const files = fs.readdirSync(uploadDir);
    const oneHourAgo = Date.now() - (60 * 60 * 1000);

    files.forEach(file => {
      const filePath = path.join(uploadDir, file);
      const stats = fs.statSync(filePath);
      
      if (stats.mtime.getTime() < oneHourAgo) {
        cleanupFile(filePath);
      }
    });
  } catch (error) {
    logger.error('Failed to cleanup old files:', error);
  }
};

// Schedule cleanup every hour
setInterval(cleanupOldFiles, 60 * 60 * 1000);
