import * as tf from '@tensorflow/tfjs-node';
import * as poseDetection from '@tensorflow-models/pose-detection';
import { logger } from '../utils/logger';
import { config } from '../config';

export interface PostureAnalysis {
  posture_type: 'squat' | 'sitting' | 'unknown';
  issues: string[];
  is_good_posture: boolean;
  knee_angle?: number;
  back_angle?: number;
  neck_angle?: number;
  landmarks_detected: boolean;
  landmarks?: any[];
  confidence_score?: number;
  timestamp: string;
  error?: string;
}

export class PostureService {
  private detector: poseDetection.PoseDetector | null = null;
  private isInitialized = false;

  async initialize(): Promise<void> {
    try {
      if (this.isInitialized) {
        logger.warn('PostureService already initialized');
        return;
      }

      logger.info('Initializing TensorFlow.js backend...');
      await tf.ready();
      
      logger.info('Loading pose detection model...');
      const model = poseDetection.SupportedModels.MoveNet;
      this.detector = await poseDetection.createDetector(model, {
        modelType: poseDetection.movenet.modelType.SINGLEPOSE_LIGHTNING
      });
      
      this.isInitialized = true;
      logger.info('PostureService initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize PostureService:', error);
      throw new Error(`PostureService initialization failed: ${error.message}`);
    }
  }

  private calculateAngle(a: [number, number], b: [number, number], c: [number, number]): number {
    const radians = Math.atan2(c[1] - b[1], c[0] - b[0]) - Math.atan2(a[1] - b[1], a[0] - b[0]);
    let angle = Math.abs(radians * 180.0 / Math.PI);
    
    if (angle > 180.0) {
      angle = 360 - angle;
    }
    
    return angle;
  }

  private analyzeSquatPosture(keypoints: poseDetection.Keypoint[]): Omit<PostureAnalysis, 'timestamp'> {
    const issues: string[] = [];
    
    try {
      // Get key landmarks with confidence check
      const getKeypoint = (name: string) => 
        keypoints.find(kp => kp.name === name && (kp.score || 0) > config.ai.confidenceThreshold);

      const leftHip = getKeypoint('left_hip');
      const leftKnee = getKeypoint('left_knee');
      const leftAnkle = getKeypoint('left_ankle');
      const leftShoulder = getKeypoint('left_shoulder');
      
      const rightHip = getKeypoint('right_hip');
      const rightKnee = getKeypoint('right_knee');
      const rightAnkle = getKeypoint('right_ankle');
      const rightShoulder = getKeypoint('right_shoulder');

      if (!leftHip || !leftKnee || !leftAnkle || !leftShoulder || 
          !rightHip || !rightKnee || !rightAnkle || !rightShoulder) {
        return {
          posture_type: 'squat',
          issues: ['Could not detect all required body landmarks with sufficient confidence'],
          is_good_posture: false,
          landmarks_detected: false,
          confidence_score: 0
        };
      }

      // Check knee over toe (knee should not go beyond ankle in x-direction)
      const kneeToeTolerance = 0.05;
      if (leftKnee.x < leftAnkle.x - kneeToeTolerance) {
        issues.push('Left knee is going too far forward over toe');
      }
      if (rightKnee.x < rightAnkle.x - kneeToeTolerance) {
        issues.push('Right knee is going too far forward over toe');
      }

      // Check back angle (should be relatively straight)
      const leftBackAngle = this.calculateAngle(
        [leftShoulder.x, leftShoulder.y],
        [leftHip.x, leftHip.y],
        [leftKnee.x, leftKnee.y]
      );
      const rightBackAngle = this.calculateAngle(
        [rightShoulder.x, rightShoulder.y],
        [rightHip.x, rightHip.y],
        [rightKnee.x, rightKnee.y]
      );
      
      const minBackAngle = 150;
      if (leftBackAngle < minBackAngle || rightBackAngle < minBackAngle) {
        issues.push('Back is too hunched forward - keep chest up and maintain straight back');
      }

      // Check knee angle for depth
      const leftKneeAngle = this.calculateAngle(
        [leftHip.x, leftHip.y],
        [leftKnee.x, leftKnee.y],
        [leftAnkle.x, leftAnkle.y]
      );
      const rightKneeAngle = this.calculateAngle(
        [rightHip.x, rightHip.y],
        [rightKnee.x, rightKnee.y],
        [rightAnkle.x, rightAnkle.y]
      );
      
      const avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2;
      const avgBackAngle = (leftBackAngle + rightBackAngle) / 2;

      // Calculate confidence score based on landmark visibility
      const avgConfidence = keypoints
        .filter(kp => kp.score !== undefined)
        .reduce((sum, kp) => sum + (kp.score || 0), 0) / keypoints.length;

      return {
        posture_type: 'squat',
        issues,
        knee_angle: Math.round(avgKneeAngle * 100) / 100,
        back_angle: Math.round(avgBackAngle * 100) / 100,
        is_good_posture: issues.length === 0,
        landmarks_detected: true,
        landmarks: keypoints,
        confidence_score: Math.round(avgConfidence * 100) / 100
      };

    } catch (error) {
      logger.error('Error in squat analysis:', error);
      return {
        posture_type: 'squat',
        issues: ['Error analyzing squat posture'],
        is_good_posture: false,
        landmarks_detected: false,
        error: error.message
      };
    }
  }

  private analyzeSittingPosture(keypoints: poseDetection.Keypoint[]): Omit<PostureAnalysis, 'timestamp'> {
    const issues: string[] = [];
    
    try {
      // Get key landmarks with confidence check
      const getKeypoint = (name: string) => 
        keypoints.find(kp => kp.name === name && (kp.score || 0) > config.ai.confidenceThreshold);

      const nose = getKeypoint('nose');
      const leftShoulder = getKeypoint('left_shoulder');
      const rightShoulder = getKeypoint('right_shoulder');
      const leftHip = getKeypoint('left_hip');
      const rightHip = getKeypoint('right_hip');

      if (!nose || !leftShoulder || !rightShoulder || !leftHip || !rightHip) {
        return {
          posture_type: 'sitting',
          issues: ['Could not detect all required body landmarks with sufficient confidence'],
          is_good_posture: false,
          landmarks_detected: false,
          confidence_score: 0
        };
      }

      // Calculate neck angle (nose to shoulder line vs vertical)
      const avgShoulderX = (leftShoulder.x + rightShoulder.x) / 2;
      const avgShoulderY = (leftShoulder.y + rightShoulder.y) / 2;
      
      const neckAngle = Math.abs(Math.atan2(nose.x - avgShoulderX, avgShoulderY - nose.y) * 180 / Math.PI);
      
      const maxNeckAngle = 30;
      if (neckAngle > maxNeckAngle) {
        issues.push('Neck is bent too far forward - adjust screen height or move closer to screen');
      }

      // Check back straightness (shoulder to hip alignment)
      const avgHipX = (leftHip.x + rightHip.x) / 2;
      const avgHipY = (leftHip.y + rightHip.y) / 2;
      
      const backAngle = Math.abs(Math.atan2(avgShoulderX - avgHipX, avgHipY - avgShoulderY) * 180 / Math.PI);
      
      const maxBackAngle = 20;
      if (backAngle > maxBackAngle) {
        issues.push('Back is not straight - sit up straighter and engage core muscles');
      }

      // Calculate confidence score
      const avgConfidence = keypoints
        .filter(kp => kp.score !== undefined)
        .reduce((sum, kp) => sum + (kp.score || 0), 0) / keypoints.length;

      return {
        posture_type: 'sitting',
        issues,
        neck_angle: Math.round(neckAngle * 100) / 100,
        back_angle: Math.round(backAngle * 100) / 100,
        is_good_posture: issues.length === 0,
        landmarks_detected: true,
        landmarks: keypoints,
        confidence_score: Math.round(avgConfidence * 100) / 100
      };

    } catch (error) {
      logger.error('Error in sitting analysis:', error);
      return {
        posture_type: 'sitting',
        issues: ['Error analyzing sitting posture'],
        is_good_posture: false,
        landmarks_detected: false,
        error: error.message
      };
    }
  }

  async analyzeImage(imageBuffer: Buffer, postureType: string = 'auto'): Promise<PostureAnalysis> {
    if (!this.isInitialized || !this.detector) {
      throw new Error('PostureService not initialized. Call initialize() first.');
    }

    const startTime = Date.now();
    
    try {
      logger.debug('Starting pose detection analysis');
      
      // Decode image
      const imageTensor = tf.node.decodeImage(imageBuffer, 3);
      
      // Detect poses
      const poses = await this.detector.estimatePoses(imageTensor);
      
      // Clean up tensor
      imageTensor.dispose();

      if (poses.length === 0) {
        return {
          posture_type: postureType as any,
          issues: ['No person detected in image'],
          is_good_posture: false,
          landmarks_detected: false,
          confidence_score: 0,
          timestamp: new Date().toISOString()
        };
      }

      const pose = poses[0];
      const keypoints = pose.keypoints.filter(kp => kp.score && kp.score > config.ai.confidenceThreshold);

      // Auto-detect posture type if not specified
      let detectedPostureType = postureType;
      if (postureType === 'auto') {
        const leftKnee = keypoints.find(kp => kp.name === 'left_knee');
        const leftHip = keypoints.find(kp => kp.name === 'left_hip');
        const leftAnkle = keypoints.find(kp => kp.name === 'left_ankle');
        
        if (leftKnee && leftHip && leftAnkle) {
          const kneeAngle = this.calculateAngle(
            [leftHip.x, leftHip.y],
            [leftKnee.x, leftKnee.y],
            [leftAnkle.x, leftAnkle.y]
          );
          detectedPostureType = kneeAngle < 140 ? 'squat' : 'sitting';
        } else {
          detectedPostureType = 'sitting';
        }
      }

      let analysis: Omit<PostureAnalysis, 'timestamp'>;
      if (detectedPostureType === 'squat') {
        analysis = this.analyzeSquatPosture(keypoints);
      } else {
        analysis = this.analyzeSittingPosture(keypoints);
      }

      const processingTime = Date.now() - startTime;
      logger.debug(`Pose analysis completed in ${processingTime}ms`);

      return {
        ...analysis,
        timestamp: new Date().toISOString()
      };

    } catch (error) {
      logger.error('Error analyzing image:', error);
      return {
        posture_type: postureType as any,
        issues: ['Failed to analyze image'],
        is_good_posture: false,
        landmarks_detected: false,
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  public isReady(): boolean {
    return this.isInitialized && this.detector !== null;
  }
}
