import { config as dotenvConfig } from 'dotenv';

// Load environment variables
dotenvConfig();

interface Config {
  port: number;
  nodeEnv: string;
  cors: {
    origin: string | string[];
  };
  upload: {
    maxFileSize: string;
    allowedTypes: string[];
    uploadDir: string;
  };
  ai: {
    modelType: string;
    confidenceThreshold: number;
  };
  logging: {
    level: string;
  };
}

const config: Config = {
  port: parseInt(process.env.PORT || '3001', 10),
  nodeEnv: process.env.NODE_ENV || 'development',
  
  cors: {
    origin: process.env.NODE_ENV === 'production' 
      ? process.env.FRONTEND_URL || 'https://your-frontend-domain.com'
      : ['http://localhost:3000', 'http://localhost:3002', 'http://localhost:5173'],
  },
  
  upload: {
    maxFileSize: '100mb',
    allowedTypes: ['video/mp4', 'video/webm', 'video/avi', 'video/mov', 'video/quicktime'],
    uploadDir: 'uploads',
  },
  
  ai: {
    modelType: 'MoveNet',
    confidenceThreshold: 0.3,
  },
  
  logging: {
    level: process.env.LOG_LEVEL || (process.env.NODE_ENV === 'production' ? 'info' : 'debug'),
  },
};

export { config };
