import { Request, Response } from 'express';
import { PostureService } from '../services/PostureService';
import { CustomError, asyncHandler } from '../middleware/errorHandler';
import { cleanupFile } from '../middleware/uploadMiddleware';
import { logger } from '../utils/logger';

interface AnalyzeFrameRequest {
  image_data: string;
  posture_type?: 'squat' | 'sitting' | 'auto';
}

interface VideoAnalysis {
  total_frames_analyzed: number;
  good_posture_frames: number;
  bad_posture_frames: number;
  posture_score: number;
  frame_analyses: any[];
  video_duration?: number;
  analysis_duration?: number;
}

export class PostureController {
  constructor(private postureService: PostureService) {}

  public analyzeFrame = asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();
    
    try {
      const { image_data, posture_type = 'auto' }: AnalyzeFrameRequest = req.body;
      
      // Validate input
      if (!image_data) {
        throw new CustomError('No image data provided', 400);
      }

      if (!['squat', 'sitting', 'auto'].includes(posture_type)) {
        throw new CustomError('Invalid posture_type. Must be "squat", "sitting", or "auto"', 400);
      }

      // Check if service is ready
      if (!this.postureService.isReady()) {
        throw new CustomError('Pose detection service not ready', 503);
      }

      logger.debug('Processing frame analysis request', {
        postureType: posture_type,
        imageDataLength: image_data.length
      });

      // Convert base64 to buffer
      let imageBuffer: Buffer;
      try {
        const base64Data = image_data.replace(/^data:image\/[a-z]+;base64,/, '');
        imageBuffer = Buffer.from(base64Data, 'base64');
      } catch (error) {
        throw new CustomError('Invalid image data format', 400);
      }

      // Analyze the image
      const result = await this.postureService.analyzeImage(imageBuffer, posture_type);
      
      const processingTime = Date.now() - startTime;
      
      logger.info('Frame analysis completed', {
        postureType: result.posture_type,
        isGoodPosture: result.is_good_posture,
        issuesCount: result.issues.length,
        processingTime,
        confidenceScore: result.confidence_score
      });

      res.status(200).json({
        success: true,
        data: result,
        meta: {
          processing_time_ms: processingTime,
          timestamp: new Date().toISOString()
        }
      });

    } catch (error) {
      logger.error('Error in analyzeFrame:', error);
      
      if (error instanceof CustomError) {
        throw error;
      }
      
      throw new CustomError('Failed to analyze frame', 500);
    }
  });

  public analyzeVideo = asyncHandler(async (req: Request, res: Response) => {
    const startTime = Date.now();
    let filePath: string | undefined;

    try {
      const { posture_type = 'auto' } = req.body;
      
      // Validate file upload
      if (!req.file) {
        throw new CustomError('No video file provided', 400);
      }

      filePath = req.file.path;

      if (!['squat', 'sitting', 'auto'].includes(posture_type)) {
        throw new CustomError('Invalid posture_type. Must be "squat", "sitting", or "auto"', 400);
      }

      // Check if service is ready
      if (!this.postureService.isReady()) {
        throw new CustomError('Pose detection service not ready', 503);
      }

      logger.info('Processing video analysis request', {
        filename: req.file.originalname,
        size: req.file.size,
        mimetype: req.file.mimetype,
        postureType: posture_type
      });

      // For now, return a mock analysis since video processing requires additional setup
      // In a production environment, you would:
      // 1. Extract frames from video using ffmpeg
      // 2. Analyze each frame
      // 3. Aggregate results
      
      const mockAnalysis: VideoAnalysis = {
        total_frames_analyzed: 30,
        good_posture_frames: 25,
        bad_posture_frames: 5,
        posture_score: 83.33,
        frame_analyses: [
          {
            frame_number: 0,
            posture_type: posture_type === 'auto' ? 'sitting' : posture_type,
            issues: [],
            is_good_posture: true,
            landmarks_detected: true,
            confidence_score: 0.85,
            timestamp: new Date().toISOString()
          },
          {
            frame_number: 15,
            posture_type: posture_type === 'auto' ? 'sitting' : posture_type,
            issues: ['Neck is bent too far forward'],
            is_good_posture: false,
            landmarks_detected: true,
            confidence_score: 0.78,
            timestamp: new Date().toISOString()
          }
        ],
        video_duration: req.file.size / (1024 * 1024), // Rough estimate
        analysis_duration: Date.now() - startTime
      };

      const processingTime = Date.now() - startTime;

      logger.info('Video analysis completed', {
        filename: req.file.originalname,
        totalFrames: mockAnalysis.total_frames_analyzed,
        postureScore: mockAnalysis.posture_score,
        processingTime
      });

      res.status(200).json({
        success: true,
        data: mockAnalysis,
        meta: {
          processing_time_ms: processingTime,
          timestamp: new Date().toISOString(),
          note: 'This is a mock implementation. Full video processing requires additional setup.'
        }
      });

    } catch (error) {
      logger.error('Error in analyzeVideo:', error);
      
      if (error instanceof CustomError) {
        throw error;
      }
      
      throw new CustomError('Failed to analyze video', 500);
      
    } finally {
      // Clean up uploaded file
      if (filePath) {
        cleanupFile(filePath);
      }
    }
  });

  public getStatus = asyncHandler(async (req: Request, res: Response) => {
    const status = {
      service: 'Posture Detection API',
      version: '1.0.0',
      status: this.postureService.isReady() ? 'ready' : 'initializing',
      model: {
        name: 'MoveNet',
        type: 'SINGLEPOSE_LIGHTNING',
        framework: 'TensorFlow.js'
      },
      capabilities: {
        real_time_analysis: true,
        video_analysis: true,
        posture_types: ['squat', 'sitting', 'auto'],
        supported_formats: ['image/jpeg', 'image/png', 'video/mp4', 'video/webm']
      },
      timestamp: new Date().toISOString()
    };

    res.status(200).json({
      success: true,
      data: status
    });
  });
}
