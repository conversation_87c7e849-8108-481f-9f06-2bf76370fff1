{"name": "@posture-app/backend", "version": "1.0.0", "description": "Backend API for posture detection application", "main": "dist/index.js", "type": "module", "private": true, "scripts": {"dev": "bun --hot src/index.ts", "start": "node dist/index.js", "build": "tsc && bun build src/index.ts --outdir ./dist", "test": "bun test", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "rm -rf dist"}, "dependencies": {"@tensorflow/tfjs-node": "^4.22.0", "@tensorflow-models/pose-detection": "^2.1.3", "cors": "^2.8.5", "express": "^5.1.0", "helmet": "^7.1.0", "morgan": "^1.10.0", "multer": "^2.0.1", "winston": "^3.11.0"}, "devDependencies": {"@types/bun": "latest", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/morgan": "^1.9.9", "@types/multer": "^2.0.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}