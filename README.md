# 🏃‍♂️ Posture Detection App

A full-stack web application that analyzes posture in real-time using AI-powered pose detection. The app can detect bad posture during squats and desk sitting, providing instant feedback to help improve form and prevent injuries.

## 🚀 Live Demo

**Deployed Application:** [Coming Soon - Will be deployed to Vercel/Railway]

**Demo Video:** [Coming Soon - Will be uploaded to YouTube/Loom]

## 🛠️ Tech Stack

### Frontend
- **React 19** - Modern React with hooks
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Bun** - Fast JavaScript runtime and package manager
- **Radix UI** - Accessible component primitives

### Backend
- **Node.js/Bun** - JavaScript runtime
- **Express.js** - Web application framework
- **TypeScript** - Type-safe server development
- **TensorFlow.js** - Machine learning in JavaScript
- **MediaPipe Models** - Pose detection via @tensorflow-models/pose-detection

### AI/ML
- **TensorFlow.js** - Client-side and server-side ML
- **MoveNet** - Lightweight pose detection model
- **Rule-based Analysis** - Custom posture evaluation logic

## 🎯 Features

### Real-time Analysis
- **Webcam Integration** - Live camera feed with real-time posture analysis
- **Instant Feedback** - Immediate detection of posture issues
- **Visual Indicators** - Clear visual feedback for good/bad posture

### Video Upload Analysis
- **File Upload** - Support for MP4, WebM, AVI, MOV formats
- **Batch Processing** - Analyze entire videos frame by frame
- **Summary Reports** - Overall posture score and detailed breakdown

### Posture Detection
- **Squat Analysis**
  - Knee-over-toe detection
  - Back angle monitoring
  - Depth assessment
- **Sitting Posture Analysis**
  - Forward head posture detection
  - Slouching identification
  - Neck angle monitoring

### Smart Features
- **Auto-detection** - Automatically determines if user is squatting or sitting
- **Manual Mode Selection** - Override auto-detection for specific analysis
- **Responsive Design** - Works on desktop and mobile devices

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │
│   (React)       │◄──►│   (Express)     │
│   Port: 3002    │    │   Port: 3001    │
└─────────────────┘    └─────────────────┘
                              │
                              ▼
                    ┌─────────────────┐
                    │  TensorFlow.js  │
                    │   MoveNet       │
                    │ Pose Detection  │
                    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites
- **Bun** (latest version) - [Install Bun](https://bun.sh/)
- **Node.js** 18+ (if not using Bun)
- **Git**

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Realfy_Oasis
   ```

2. **Install Backend Dependencies**
   ```bash
   cd backend
   bun install
   ```

3. **Install Frontend Dependencies**
   ```bash
   cd ../frontend
   bun install
   ```

### Development

1. **Start the Backend Server**
   ```bash
   cd backend
   bun run dev
   ```
   Backend will run on `http://localhost:3001`

2. **Start the Frontend Server** (in a new terminal)
   ```bash
   cd frontend
   bun run dev
   ```
   Frontend will run on `http://localhost:3002`

3. **Open your browser**
   Navigate to `http://localhost:3002` to use the application

### Production Build

1. **Build Backend**
   ```bash
   cd backend
   bun run build
   ```

2. **Build Frontend**
   ```bash
   cd frontend
   bun run build
   ```

## 📖 Usage Guide

### For Squat Analysis
1. Select "Squat" mode or use "Auto Detect"
2. Position yourself sideways to the camera
3. Ensure your full body is visible in the frame
4. Start analysis and perform squats
5. Watch for real-time feedback on:
   - Knee position relative to toes
   - Back straightness
   - Squat depth

### For Sitting Posture Analysis
1. Select "Sitting" mode or use "Auto Detect"
2. Sit facing the camera
3. Ensure your upper body is clearly visible
4. Start analysis while maintaining normal posture
5. Monitor feedback for:
   - Forward head posture
   - Slouching detection
   - Neck angle

### Video Upload Analysis
1. Switch to "Upload" mode
2. Select a video file (max 100MB)
3. Choose posture type
4. Click "Analyze Video"
5. Review comprehensive analysis results

## 🔧 API Endpoints

### Backend API (`http://localhost:3001`)

- `GET /health` - Health check endpoint
- `POST /api/analyze-frame` - Analyze single image frame
- `POST /api/analyze-video` - Analyze uploaded video file

## 🎨 Rule-Based Posture Logic

### Squat Analysis Rules
- **Knee-over-toe**: Knee should not extend beyond ankle in forward direction
- **Back angle**: Torso should maintain 150°+ angle with thighs
- **Depth**: Knee angle assessment for proper squat depth

### Sitting Analysis Rules
- **Neck angle**: Head should not be more than 30° forward from vertical
- **Back straightness**: Shoulder-to-hip alignment within 20° of vertical

## 🚀 Deployment

The application is designed to be deployed on modern platforms:

- **Frontend**: Vercel, Netlify, or similar static hosting
- **Backend**: Railway, Render, Heroku, or similar Node.js hosting
- **Environment Variables**: Configure API endpoints for production

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **TensorFlow.js** team for the excellent ML framework
- **MediaPipe** team for pose detection models
- **Bun** team for the fast JavaScript runtime
- **Vercel** for deployment platform

---

Built with ❤️ for the Realfy technical assignment
