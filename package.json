{"name": "posture-detection-app", "version": "1.0.0", "description": "AI-powered posture detection application for squats and desk sitting", "private": true, "workspaces": ["packages/frontend", "packages/backend"], "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "cd packages/backend && npm run dev", "dev:frontend": "cd packages/frontend && npm run dev", "build": "npm run build:backend && npm run build:frontend", "build:backend": "cd packages/backend && npm run build", "build:frontend": "cd packages/frontend && npm run build", "start": "npm run start:backend", "start:backend": "cd packages/backend && npm run start", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd packages/backend && npm run test", "test:frontend": "cd packages/frontend && npm run test", "lint": "npm run lint:backend && npm run lint:frontend", "lint:backend": "cd packages/backend && npm run lint", "lint:frontend": "cd packages/frontend && npm run lint", "clean": "rm -rf packages/*/dist packages/*/node_modules node_modules"}, "devDependencies": {"concurrently": "^9.2.0"}, "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/posture-detection-app.git"}, "keywords": ["posture-detection", "ai", "tensorflow", "react", "express", "computer-vision"], "author": "Your Name", "license": "MIT"}